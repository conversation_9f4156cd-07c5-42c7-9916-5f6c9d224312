#!/usr/bin/env python3
"""
Test script for the _sort_polygon_vertices function
"""

import torch
import numpy as np
import math
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

# Import the function from the parse_base module
import sys
import os
sys.path.append('src')
from Parse_mesh.parse_base import BaseMeshManager


def is_convex_polygon(vertices):
    """
    Check if a polygon is convex by verifying that all cross products have the same sign.

    Args:
        vertices: torch.Tensor of shape [N, P, 3] representing polygon vertices

    Returns:
        bool: True if polygon is convex, False otherwise
    """
    N, P, _ = vertices.shape

    for n in range(N):
        # Get vertices for this polygon
        poly_vertices = vertices[n]  # [P, 3]

        # Calculate cross products for consecutive edges
        cross_products = []
        for i in range(P):
            v1 = poly_vertices[(i + 1) % P] - poly_vertices[i]
            v2 = poly_vertices[(i + 2) % P] - poly_vertices[(i + 1) % P]
            cross_prod = torch.cross(v1, v2)
            cross_products.append(cross_prod)

        # Check if all cross products have the same sign (for convexity)
        # We'll use the z-component for 2D polygons in XY plane
        # For 3D polygons, we need to project onto the polygon plane

        # Calculate polygon normal
        edge1 = poly_vertices[1] - poly_vertices[0]
        edge2 = poly_vertices[2] - poly_vertices[0]
        normal = torch.cross(edge1, edge2)
        normal = normal / (torch.norm(normal) + 1e-8)

        # Project cross products onto normal and check signs
        signs = []
        for cp in cross_products:
            projection = torch.dot(cp, normal)
            signs.append(projection.item())

        # Check if all signs are the same (all positive or all negative)
        positive_signs = sum(1 for s in signs if s > 1e-8)
        negative_signs = sum(1 for s in signs if s < -1e-8)

        if positive_signs > 0 and negative_signs > 0:
            return False  # Mixed signs indicate non-convex

    return True


def create_test_polygons():
    """Create test polygons for validation"""

    # Test case 1: Simple square in XY plane (randomly ordered)
    square_vertices = torch.tensor([
        [[-1.0, -1.0, 0.0], # Bottom-left
         [1.0, 1.0, 0.0],   # Top-right
         [-1.0, 1.0, 0.0],  # Top-left
         [1.0, -1.0, 0.0]]  # Bottom-right
    ], dtype=torch.float64)

    # Test case 2: Triangle in XY plane (randomly ordered)
    triangle_vertices = torch.tensor([
        [[-0.5, -0.866, 0.0], # Bottom-left
         [-0.5, 0.866, 0.0],  # Top-left
         [1.0, 0.0, 0.0]]     # Right
    ], dtype=torch.float64)

    # Test case 3: Pentagon in XY plane (randomly ordered)
    pentagon_vertices = torch.tensor([
        [[-0.809, -0.588, 0.0],  # Bottom-left
         [0.309, 0.951, 0.0],    # Top-right
         [1.0, 0.0, 0.0],        # Right
         [-0.809, 0.588, 0.0],   # Top-left
         [0.309, -0.951, 0.0]]   # Bottom-right
    ], dtype=torch.float64)

    # Test case 4: Hexagon in a tilted plane (randomly ordered)
    # Create hexagon in XY plane first, then rotate
    hexagon_2d = []
    for i in [3, 0, 4, 1, 5, 2]:  # Random order
        x = math.cos(i * math.pi / 3)
        y = math.sin(i * math.pi / 3)
        z = 0.0
        hexagon_2d.append([x, y, z])

    # Rotate around X axis by 45 degrees
    rotation_angle = math.pi / 4
    cos_a, sin_a = math.cos(rotation_angle), math.sin(rotation_angle)
    hexagon_3d = []
    for x, y, z in hexagon_2d:
        new_y = y * cos_a - z * sin_a
        new_z = y * sin_a + z * cos_a
        hexagon_3d.append([x, new_y, new_z])

    hexagon_vertices = torch.tensor([hexagon_3d], dtype=torch.float64)

    return {
        'square': square_vertices,
        'triangle': triangle_vertices,
        'pentagon': pentagon_vertices,
        'hexagon': hexagon_vertices
    }


def test_sort_polygon_vertices():
    """Test the _sort_polygon_vertices function"""
    
    # Create a dummy mesh manager instance to access the method
    class TestMeshManager(BaseMeshManager):
        def _extract_base_mesh_data(self):
            pass
        def _calculate_connectivity_data(self):
            pass
    
    mesh_manager = TestMeshManager()
    
    # Get test polygons
    test_polygons = create_test_polygons()
    
    print("Testing _sort_polygon_vertices function...")
    print("=" * 50)
    
    for name, vertices in test_polygons.items():
        print(f"\nTesting {name}:")
        print(f"Original vertices shape: {vertices.shape}")
        print(f"Original vertices:\n{vertices}")
        
        # Calculate centroid
        centroid = vertices.mean(dim=1, keepdim=True)
        print(f"Centroid: {centroid.squeeze()}")
        
        # Sort vertices
        try:
            sorted_vertices = mesh_manager._sort_polygon_vertices(vertices)
            print(f"Sorted vertices:\n{sorted_vertices}")
            
            # Calculate angles to verify sorting
            CP = vertices - centroid
            pCP = CP[:, 0:1, :]
            
            # Calculate normal vector
            v1 = vertices[:, 1, :] - vertices[:, 0, :]
            v2 = vertices[:, 2, :] - vertices[:, 0, :]
            normal = torch.cross(v1, v2, dim=1)
            normal = normal / (torch.norm(normal, dim=1, keepdim=True) + 1e-8)
            
            # Calculate angles for original vertices
            angles_orig = []
            for i in range(vertices.shape[1]):
                current_CP = CP[:, i, :]
                dot_product = torch.sum(pCP.squeeze(1) * current_CP, dim=1)
                cross_product = torch.cross(pCP.squeeze(1), current_CP, dim=1)
                cross_magnitude = torch.sum(cross_product * normal, dim=1)
                angle = torch.atan2(cross_magnitude, dot_product)
                angle = torch.where(angle < 0, angle + 2 * math.pi, angle)
                angles_orig.append(angle.item())
            
            print(f"Original angles (degrees): {[math.degrees(a) for a in angles_orig]}")
            
            # Calculate angles for sorted vertices
            CP_sorted = sorted_vertices - centroid
            angles_sorted = []
            for i in range(sorted_vertices.shape[1]):
                current_CP = CP_sorted[:, i, :]
                dot_product = torch.sum(pCP.squeeze(1) * current_CP, dim=1)
                cross_product = torch.cross(pCP.squeeze(1), current_CP, dim=1)
                cross_magnitude = torch.sum(cross_product * normal, dim=1)
                angle = torch.atan2(cross_magnitude, dot_product)
                angle = torch.where(angle < 0, angle + 2 * math.pi, angle)
                angles_sorted.append(angle.item())
            
            print(f"Sorted angles (degrees): {[math.degrees(a) for a in angles_sorted]}")
            
            # Verify that angles are in ascending order
            is_sorted = all(angles_sorted[i] <= angles_sorted[i+1] for i in range(len(angles_sorted)-1))
            print(f"Angles properly sorted: {is_sorted}")
            
            # Check if the sorted polygon is convex
            is_convex_orig = is_convex_polygon(vertices)
            is_convex_sorted = is_convex_polygon(sorted_vertices)
            print(f"Original polygon convex: {is_convex_orig}")
            print(f"Sorted polygon convex: {is_convex_sorted}")

            # Verify angles are in [0, 2π] range
            angles_in_range = all(0 <= a <= 2 * math.pi + 1e-6 for a in angles_sorted)
            print(f"All angles in [0, 2π] range: {angles_in_range}")

        except Exception as e:
            print(f"Error processing {name}: {e}")
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    test_sort_polygon_vertices()
