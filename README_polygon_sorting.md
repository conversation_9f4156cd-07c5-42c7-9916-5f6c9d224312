# Polygon Vertex Sorting Function

## Overview

The `_sort_polygon_vertices` function in `src/Parse_mesh/parse_base.py` sorts vertices of polygons in counterclockwise order around their centroids, ensuring they form convex polygons.

## Function Signature

```python
def _sort_polygon_vertices(self, vertices_positions: torch.Tensor) -> torch.Tensor:
    """
    Sort vertices of a polygon in counterclockwise order around the face center.

    Args:
        vertices_positions: Tensor of shape [N, P, 3] where N is number of polygons,
                          P is number of vertices per polygon, 3 is spatial dimensions

    Returns:
        Sorted vertices_positions tensor with same shape [N, P, 3]
    """
```

## Key Features

1. **Vectorized Implementation**: No for loops, uses PyTorch vectorized operations for high performance
2. **Batch Processing**: Can process multiple polygons simultaneously (N polygons)
3. **3D Space Support**: Works with polygons in any 3D plane by calculating normal vectors
4. **Angle Range Guarantee**: All angles are in [0, 2π] range
5. **Pivot Vector Guarantee**: First angle is always 0° (pivot vector with itself)
6. **Numerical Stability**: Handles edge cases like angles close to 2π
7. **Convex Polygon Output**: Ensures sorted vertices form convex polygons

## Algorithm Steps

1. **Calculate Centroid**: `poly_centroid = vertices_positions.mean(dim=1, keepdim=True)`
2. **Compute Vectors**: `CP = vertices_positions - poly_centroid` (centroid to vertices)
3. **Set Pivot**: `pCP = CP[:, 0:1, :]` (first vertex as reference)
4. **Calculate Normal**: Use first 3 vertices to compute plane normal vector
5. **Compute Angles**: Calculate angles between pivot and all vectors using atan2
6. **Sort**: Sort vertices by ascending angle order
7. **Return**: Sorted vertices maintaining convex polygon property

## Usage Example

```python
# Create mesh manager instance
mesh_manager = YourMeshManager()  # Subclass of BaseMeshManager

# Input: Random ordered polygon vertices [N, P, 3]
vertices = torch.tensor([
    [[-1.0, -1.0, 0.0],   # Bottom-left
     [1.0, 1.0, 0.0],     # Top-right  
     [-1.0, 1.0, 0.0],    # Top-left
     [1.0, -1.0, 0.0]]    # Bottom-right
], dtype=torch.float64)

# Sort vertices
sorted_vertices = mesh_manager._sort_polygon_vertices(vertices)

# Output: Properly ordered convex polygon
```

## Running Unit Tests

The function includes comprehensive unit tests with 3D visualization:

```bash
# Run tests with visualization
conda activate your_env
cd /path/to/Gen-FVGN-3D
python src/Parse_mesh/parse_base.py
```

### Test Cases

1. **Square**: Random vertex order → Convex square
2. **Triangle**: Already ordered → Maintains order  
3. **Pentagon**: Random vertex order → Convex pentagon
4. **Hexagon**: Random vertex order in tilted 3D plane → Convex hexagon

### Test Output

- Console output with detailed angle calculations
- 3D visualization saved as `polygon_sorting_comparison.png`
- Interactive matplotlib plot (if available)
- Verification of convexity, angle sorting, and numerical ranges

## Dependencies

- **Required**: `torch`, `numpy`, `math`
- **Optional**: `matplotlib` (for visualization)

## Performance

- **Vectorized**: O(N*P) complexity for N polygons with P vertices each
- **Memory Efficient**: In-place operations where possible
- **GPU Compatible**: Works with CUDA tensors

## Validation

All test cases verify:
- ✅ Angles properly sorted (monotonic increasing)
- ✅ First angle is 0° (pivot vector property)
- ✅ Non-convex polygons become convex
- ✅ All angles in [0, 2π] range
- ✅ Support for different polygon types and 3D orientations
